import React, { useState, useEffect } from 'react';
import { Check, X, Download, Eye, Filter, Play, Mail, Loader2 } from 'lucide-react';
import type { Booking, Zone, Transaction } from '../types';
import { supabase } from '../supabaseClient';
import { sendEmail, getBookingApprovedEmailTemplate, getAdminNotificationEmailTemplate, setEmailConfig } from '../utils/emailService';
import EmailConfigModal from '../components/EmailConfigModal';

export default function Bookings() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
  const [isEmailConfigModalOpen, setIsEmailConfigModalOpen] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    bookingId: string;
    action: 'approve' | 'reject' | 'fulfill';
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: {
      start: '',
      end: ''
    },
    zone: '',
    status: ''
  });
  const [emailConfigured, setEmailConfigured] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [adminEmail, setAdminEmail] = useState<string>('');

  // Helper function to format date for comparison
  const formatDate = (dateString: string) => {
    return new Date(dateString).toISOString().split('T')[0];
  };

  // Helper function to check if a date is within range
  const isDateInRange = (date: string, start: string, end: string) => {
    const checkDate = new Date(date);
    const startDate = start ? new Date(start) : null;
    const endDate = end ? new Date(end) : null;

    if (!startDate && !endDate) return true;
    if (startDate && !endDate) return checkDate >= startDate;
    if (!startDate && endDate) return checkDate <= endDate;
    return checkDate >= startDate! && checkDate <= endDate!;
  };

  useEffect(() => {
    fetchBookings();

    // Pre-configure email settings with the provided credentials
    configureEmailSettings(
      'smtp.gmail.com',  // SMTP host
      587,               // SMTP port
      false,             // Secure (use TLS)
      '<EMAIL>', // Sender email
      'nlnk wltw rgsp zhpp',  // App password
      '<EMAIL>'   // Admin email for notifications
    );
  }, []);

  const fetchBookings = async () => {
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          user_id,
          zone_id,
          start_date,
          end_date,
          listing_type,
          business_name,
          description,
          phone_number,
          email,
          video_url,
          status,
          payment_id,
          created_at,
          updated_at,
          zones (
            id,
            name,
            city,
            sub_zone,
            pincode,
            description,
            price_year,
            image_url,
            isavailable
          ),
          booking_zones (
            zone_id,
            zones (
              id,
              name,
              city,
              sub_zone
            )
          ),
          users (
            id,
            email
          ),
          transactions (*)
        `)
        .returns<Booking[]>();

      if (error) {
        throw error;
      }

      if (data) {
        setBookings(data);
      }
    } catch (error: any) {
      console.error("Error fetching bookings:", error.message);
    }
  };

  // Configure email settings - this would be called when admin provides credentials
  const configureEmailSettings = (host: string, port: number, secure: boolean, user: string, pass: string, adminEmailAddress: string) => {
    try {
      setEmailConfig({
        host,
        port,
        secure,
        auth: { user, pass }
      }, adminEmailAddress);

      setAdminEmail(adminEmailAddress); // Set the admin email in state
      setEmailConfigured(true);
      setEmailError(null);
      return true;
    } catch (error: any) {
      setEmailError(error.message);
      return false;
    }
  };

  // Send approval email to customer
  const sendApprovalEmail = async (booking: Booking) => {
    if (!emailConfigured) {
      console.warn('Email not configured yet');
      return false;
    }

    try {
      const customerEmail = booking.email;
      if (!customerEmail) {
        console.error('Customer email not found in booking');
        return false;
      }

      const zoneName = booking.zones?.name || booking.booking_zones?.[0]?.zones.name || 'Unknown Zone';

      // Send email to customer
      const emailSent = await sendEmail({
        to: customerEmail,
        subject: 'Your Booking Has Been Approved',
        html: getBookingApprovedEmailTemplate(
          booking.business_name,
          zoneName,
          booking.start_date,
          booking.end_date
        )
      }, booking.id);

      // Send notification to admin using the state variable
      if (emailSent && adminEmail) {
        await sendEmail({
          to: adminEmail,
          subject: 'Booking Approval Notification',
          html: getAdminNotificationEmailTemplate(
            booking.business_name,
            zoneName,
            booking.start_date,
            booking.end_date,
            customerEmail
          )
        }, booking.id);
      }

      return emailSent;
    } catch (error: any) {
      console.error('Error sending approval email:', error);
      return false;
    }
  };

  const handleStatusChange = async (bookingId: string, newStatus: Booking['status']) => {
    setIsProcessing(true);

    try {
      const { error } = await supabase
        .from('bookings')
        .update({ status: newStatus })
        .eq('id', bookingId);

      if (error) {
        throw error;
      }

      // Update local state
      const updatedBookings = bookings.map(booking =>
        booking.id === bookingId ? { ...booking, status: newStatus } : booking
      );
      setBookings(updatedBookings);

      // Send email notification for approval
      if (newStatus === 'Approved') {
        const approvedBooking = updatedBookings.find(b => b.id === bookingId);
        if (approvedBooking && emailConfigured) {
          await sendApprovalEmail(approvedBooking);
        }
      }

      setIsConfirmationOpen(false);
      setPendingAction(null);
    } catch (error: any) {
      console.error("Error updating booking status:", error.message);
      // You might want to show an error message to the user here
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewDetails = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const handleViewVideo = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsVideoModalOpen(true);
  };

  const handleActionClick = (bookingId: string, action: 'approve' | 'reject' | 'fulfill') => {
    const booking = bookings.find(b => b.id === bookingId);
    if (booking) {
      setSelectedBooking(booking);
      setPendingAction({ bookingId, action });
      setIsProcessing(false); // Reset processing state when opening modal
      setIsConfirmationOpen(true);
    }
  };

  const getStatusColor = (status: Booking['status']) => {
    switch (status) {
      case 'Approved': // Capitalized
        return 'bg-green-100 text-green-800';
      case 'Pending': // Capitalized
        return 'bg-yellow-100 text-yellow-800';
      case 'Fulfilled': // Capitalized
        return 'bg-blue-100 text-blue-800';
      case 'Cancelled': // Capitalized
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

 const handleExportCSV = () => {
    const headers = ['ID', 'Customer', 'Business', 'Zone', 'Start Date', 'End Date', 'Status', 'Video URL'];
    const csvContent = [
      headers.join(','),
      ...bookings.map(booking => [
        booking.id,
        booking.business_name,
        booking.zones?.name,
        booking.start_date,
        booking.end_date,
        booking.status,
        booking.video_url || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bookings-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredBookings = bookings.filter(booking => {
    // Zone filter
    const matchesZone = !filters.zone ||
      booking.zone_id === filters.zone ||
      booking.booking_zones?.some(bz => bz.zone_id === filters.zone);

    // Status filter - case insensitive comparison
    const matchesStatus = !filters.status ||
      booking.status.toLowerCase() === filters.status.toLowerCase();

    // Date range filter
    const matchesDate = isDateInRange(
      booking.start_date,
      filters.dateRange.start,
      filters.dateRange.end
    );

    return matchesZone && matchesStatus && matchesDate;
  });

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Booking Management</h1>
          <div className="flex space-x-3">
            <button
              onClick={handleExportCSV}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              <Download className="h-5 w-5 mr-2" />
              Export CSV
            </button>
          </div>
        </div>
        {emailConfigured && (
          <div className="bg-blue-50 p-3 rounded-md">
            <p className="text-sm text-blue-700">
              <strong>Email Configuration:</strong> Emails will be sent from <span className="font-mono"><EMAIL></span> and admin notifications will be sent to <span className="font-mono"><EMAIL></span>.
            </p>
            <p className="text-sm text-blue-700 mt-1">
              <strong>Note:</strong> Emails are currently in simulation mode. See the <a href="/docs/email-implementation-guide.md" target="_blank" className="underline">implementation guide</a> for connecting to a real email service.
            </p>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex items-center space-x-4">
          <Filter className="h-5 w-5 text-gray-400" />
          <h2 className="text-lg font-medium">Filters</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Start Date</label>
            <input
              type="date"
              value={filters.dateRange.start}
              onChange={(e) => setFilters({
                ...filters,
                dateRange: { ...filters.dateRange, start: e.target.value }
              })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">End Date</label>
            <input
              type="date"
              value={filters.dateRange.end}
              onChange={(e) => setFilters({
                ...filters,
                dateRange: { ...filters.dateRange, end: e.target.value }
              })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Zone</label>
            <select
              value={filters.zone}
              onChange={(e) => setFilters({ ...filters, zone: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">All Zones</option>
              {Array.from(
                new Set([
                  ...bookings.map(b => b.zones).filter(Boolean),
                  ...bookings.flatMap(b => b.booking_zones?.map(bz => bz.zones) || [])
                ])
              ).map(zone => (
                <option key={zone.id} value={zone.id}>
                  {zone.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">All Status</option>
              <option value="Pending">Pending</option>
              <option value="Approved">Approved</option>
              <option value="Fulfilled">Fulfilled</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Add Clear Filters button */}
        <div className="mt-4 flex justify-end">
          <button
            onClick={() => setFilters({
              dateRange: { start: '', end: '' },
              zone: '',
              status: ''
            })}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Bookings Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Zone
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dates
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredBookings.map(booking => (
              <tr key={booking.id}>
                <td className="px-6 py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {booking.business_name}
                     </div>
                     <div className="text-sm text-gray-500">
                       {booking.email || booking.users?.email || 'No email available'}
                     </div>
                     {/* Add phone number display */}
                     <div className="text-sm text-gray-500">
                       {booking.phone_number}
                     </div>
                   </div>
                 </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {booking.booking_zones?.map(bz => bz.zones.name).join(', ') || booking.zones?.name}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  <div>{booking.start_date}</div>
                  <div>{booking.end_date}</div>
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                    {booking.status?.charAt(0).toUpperCase() + booking.status?.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4 text-right text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleViewDetails(booking)}
                    className="text-primary-600 hover:text-primary-900"
                  >
                    <Eye className="h-5 w-5" />
                  </button>
                  {booking.video_url && (
                    <button
                      onClick={() => handleViewVideo(booking)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Play className="h-5 w-5" />
                    </button>
                  )}
                  {booking.status === 'Pending' && ( // Capitalized
                    <>
                      <button
                        onClick={() => handleActionClick(booking.id, 'approve')}
                        className="text-green-600 hover:text-green-900"
                      >
                        <Check className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleActionClick(booking.id, 'reject')}
                        className="text-red-600 hover:text-red-900"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </>
                  )}
                  {booking.status === 'Approved' && ( // Capitalized
                    <button
                      onClick={() => handleActionClick(booking.id, 'fulfill')}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Check className="h-5 w-5" />
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Booking Details Modal */}
      {isModalOpen && selectedBooking && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-lg w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Booking Details
                </h3>
                <div className="space-y-4">
                   <div>
                     <h4 className="text-sm font-medium text-gray-500">Customer Information</h4>
                     <p className="mt-1 text-sm text-gray-900">{selectedBooking.business_name}</p>
                     <p className="mt-1 text-sm text-gray-500">{selectedBooking.email || selectedBooking.users?.email || 'No email available'}</p>
                     {/* Add phone number display */}
                     <p className="mt-1 text-sm text-gray-500">{selectedBooking.phone_number}</p>
                   </div>
                   <div>
                    <h4 className="text-sm font-medium text-gray-500">Description</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedBooking.description}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Zones</h4>
                    <div className="mt-1 space-y-1">
                      {selectedBooking.booking_zones?.map(bz => (
                        <div key={bz.zone_id} className="text-sm text-gray-900">
                          <p>{bz.zones.name}</p>
                          <p className="text-xs text-gray-500">
                            {bz.zones.city} - {bz.zones.sub_zone}
                          </p>
                        </div>
                      )) || (
                        <p className="text-sm text-gray-900">{selectedBooking.zones?.name}</p>
                      )}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Dates</h4>
                    <p className="mt-1 text-sm text-gray-900">
                      From: {selectedBooking.start_date}<br />
                      To: {selectedBooking.end_date}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Status</h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedBooking.status)}`}>
                      {selectedBooking.status?.charAt(0).toUpperCase() + selectedBooking.status?.slice(1)}
                    </span>
                  </div>
                  {/* Display Transaction Details */}
                  {selectedBooking.transactions && selectedBooking.transactions.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Payment Details</h4>
                      <p className="mt-1 text-sm text-gray-900">
                        Payment ID: {selectedBooking.transactions[0].razorpay_payment_id}
                      </p>
                      <p className="mt-1 text-sm text-gray-900">
                        Amount: ₹{selectedBooking.transactions[0].amount?.toLocaleString('en-IN')} ({selectedBooking.transactions[0].currency})
                      </p>
                      <p className="mt-1 text-sm text-gray-500">
                        Method: {selectedBooking.transactions[0].method || 'N/A'}
                      </p>
                      <p className="mt-1 text-sm text-gray-500">
                        Status: {selectedBooking.transactions[0].status || 'N/A'}
                      </p>
                    </div>
                  )}
                  {selectedBooking.video_url && ( // Corrected variable name here
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Advertisement Video</h4>
                      <button
                        onClick={() => {
                          setIsModalOpen(false);
                          handleViewVideo(selectedBooking);
                        }}
                        className="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        View Video
                      </button>
                    </div>
                  )}
                </div>
                <div className="mt-6 flex justify-end space-x-3">
                  {selectedBooking.status === 'Pending' && ( // Capitalized
                    <>
                      <button
                        onClick={() => {
                          setIsModalOpen(false);
                          handleActionClick(selectedBooking.id, 'approve');
                        }}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                      >
                        Approve
                      </button>
                      <button
                        onClick={() => {
                          setIsModalOpen(false);
                          handleActionClick(selectedBooking.id, 'reject');
                        }}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                      >
                        Reject
                      </button>
                    </>
                  )}
                  {selectedBooking.status === 'Approved' && ( // Capitalized
                    <button
                      onClick={() => {
                        setIsModalOpen(false);
                        handleActionClick(selectedBooking.id, 'fulfill');
                      }}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Mark as Fulfilled
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Video Modal */}
      {isVideoModalOpen && selectedBooking && selectedBooking.video_url && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-900 bg-opacity-95 transition-opacity" />
            <div className="relative bg-black rounded-lg max-w-4xl w-full aspect-video">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={() => setIsVideoModalOpen(false)}
                  className="text-white hover:text-gray-300"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <video
                className="w-full h-full rounded-lg"
                controls
                autoPlay
                src={selectedBooking.video_url}
              >
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {isConfirmationOpen && selectedBooking && pendingAction && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-lg w-full">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Confirm {pendingAction.action === 'approve' ? 'Approval' : pendingAction.action === 'fulfill' ? 'Fulfillment' : 'Rejection'}
                </h3>
                <div className="space-y-4">
                  <p className="text-sm text-gray-500">
                    Are you sure you want to {pendingAction.action} the booking for:
                  </p>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="font-medium text-gray-900">{selectedBooking.business_name}</p>
                    <p className="text-sm text-gray-500">{selectedBooking.zones?.name}</p>
                    <p className="text-sm text-gray-500">
                      {selectedBooking.start_date} to {selectedBooking.end_date}
                    </p>
                  </div>
                  <p className="text-sm text-gray-500">
                    {pendingAction.action === 'approve'
                      ? emailConfigured
                        ? 'An email will be automatically sent to notify the customer.'
                        : 'Note: Email is not configured. No notification will be sent.'
                      : pendingAction.action === 'fulfill'
                      ? 'This will mark the booking as completed.'
                      : 'The customer will be notified of the rejection via email.'}
                  </p>
                  {pendingAction.action === 'approve' && !emailConfigured && (
                    <div className="mt-2">
                      <button
                        onClick={() => {
                          setIsConfirmationOpen(false);
                          setPendingAction(null);
                          setIsEmailConfigModalOpen(true);
                        }}
                        className="text-primary-600 hover:text-primary-800 text-sm font-medium"
                      >
                        Configure Email Settings
                      </button>
                    </div>
                  )}
                </div>
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => {
                      setIsConfirmationOpen(false);
                      setPendingAction(null);
                      setIsProcessing(false); // Reset processing state when canceling
                    }}
                    disabled={isProcessing}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleStatusChange(
                      pendingAction.bookingId,
                      pendingAction.action === 'approve' ? 'Approved' : // Capitalized
                      pendingAction.action === 'fulfill' ? 'Fulfilled' : 'Cancelled' // Capitalized
                    )}
                    disabled={isProcessing}
                    className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed ${
                      pendingAction.action === 'approve'
                        ? 'bg-green-600 hover:bg-green-700 disabled:hover:bg-green-600'
                        : pendingAction.action === 'fulfill'
                        ? 'bg-blue-600 hover:bg-blue-700 disabled:hover:bg-blue-600'
                        : 'bg-red-600 hover:bg-red-700 disabled:hover:bg-red-600'
                    }`}
                  >
                    {isProcessing && (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    {isProcessing
                      ? (pendingAction.action === 'approve'
                          ? 'Processing Approval...'
                          : pendingAction.action === 'fulfill'
                          ? 'Processing Fulfillment...'
                          : 'Processing Rejection...')
                      : (pendingAction.action === 'approve'
                          ? 'Confirm Approval'
                          : pendingAction.action === 'fulfill'
                          ? 'Confirm Fulfillment'
                          : 'Confirm Rejection')
                    }
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Email Configuration Modal */}
      <EmailConfigModal
        isOpen={isEmailConfigModalOpen}
        onClose={() => setIsEmailConfigModalOpen(false)}
        onSave={configureEmailSettings}
      />
    </div>
  );
}
